using MetroFramework.Components;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using OCRTools.Common;
using OCRTools.OtherExt.MetroFramework.Forms;
using OCRTools.Properties;
using OCRTools.UserControlEx;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Linq;
using System.Security;
using System.Threading;
using System.Windows.Forms;
using static MetroFramework.Forms.MetroForm;

namespace MetroFramework.Forms
{
    public class MetroForm : BaseForm, IMetroForm
    {
        private MetroColorStyle _metroStyle = MetroCommonStyle.DefaultStyle;

        private MetroThemeStyle _metroTheme = MetroThemeStyle.Light;

        private Form _shadowForm;
        private MetroFormShadowType _shadowType = MetroFormShadowType.Flat;

        private Dictionary<WindowButtons, List<Control>> _windowButtonList = new Dictionary<WindowButtons, List<Control>>();

        public MetroForm()
        {
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.ResizeRedraw |
                ControlStyles.DoubleBuffer |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.ContainerControl |
                ControlStyles.SupportsTransparentBackColor
                , true);

            base.FormBorderStyle = FormBorderStyle.None;
            Name = "MetroForm";
            StartPosition = FormStartPosition.CenterScreen;
            AutoScaleMode = CommonString.CommonAutoScaleMode;

            var newUnit = CommonString.CommonGraphicsUnit();
            if (Font.Unit != newUnit)
            {
                var size = newUnit == GraphicsUnit.Pixel ? Font.Size * 1.33F : Font.Size / 1.33F;
                Font = new Font(Font.FontFamily.Name, size, Font.Style, newUnit);
            }
        }

        public virtual void OnThemeChange()
        {
            // 延迟再次应用样式，确保所有控件都已完全加载
            BeginInvoke(new Action(() =>
            {
                ApplyThemeToAllControls(this);
            }));
        }

        [Browsable(false)] public override Color BackColor => MetroPaint.BackColor.Form(Theme);

        private const int borderWidth = 5;

        [Category("Metro Appearance")] public bool Movable { get; set; } = true;

        public new bool TopMost
        {
            get => base.TopMost;
            set
            {
                base.TopMost = value;
            }
        }

        public new Padding Padding
        {
            get => base.Padding;
            set
            {
                value.Top = Math.Max((int)Math.Round(value.Top * CommonTheme.DpiScale), DefaultPadding.Top);
                base.Padding = value;
            }
        }

        protected override Padding DefaultPadding => new Padding(
            (int)Math.Round(20 * CommonTheme.DpiScale), // 左右边距20px
            (int)Math.Round(55 * CommonTheme.DpiScale), // 恢复到50px，平衡美观和实用性
            (int)Math.Round(20 * CommonTheme.DpiScale), // 左右边距20px
            (int)Math.Round(20 * CommonTheme.DpiScale)); // 底部边距20px

        [Category("Metro Appearance")] public bool Resizable { get; set; } = true;

        [DefaultValue(MetroFormShadowType.Flat)]
        [Category("Metro Appearance")]
        public MetroFormShadowType ShadowType
        {
            get
            {
                if (!IsMdiChild) return _shadowType;
                return MetroFormShadowType.None;
            }
            set => _shadowType = value;
        }

        const int WS_EX_COMPOSITED = 0x02000000;

        /// <summary>
        /// 是否在窗体显示后移除 WS_EX_COMPOSITED 样式，避免与 WebView2 等控件的 GPU 渲染冲突
        /// 默认为 true，可在子类构造函数中设置为 false 来禁用此功能
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        protected bool AutoRemoveCompositedStyle { get; set; } = true;

        protected override CreateParams CreateParams
        {
            get
            {
                var createParams = base.CreateParams;
                createParams.ExStyle |= WS_EX_COMPOSITED;
                createParams.Style |= 131072;
                if (ShadowType == MetroFormShadowType.SystemShadow) createParams.ClassStyle |= 131072;
                return createParams;
            }
        }

        #region Window Style Management

        /// <summary>
        /// 移除 WS_EX_COMPOSITED 样式，避免与 WebView2 等控件的 GPU 渲染冲突
        /// </summary>
        private void RemoveCompositedStyle()
        {
            if (!IsHandleCreated) return;

            try
            {
                // 直接移除 WS_EX_COMPOSITED 样式
                var currentExStyle = GetWindowLong(Handle, -20); // GWL_EXSTYLE
                var newExStyle = currentExStyle & ~WS_EX_COMPOSITED;
                SetWindowLong(Handle, -20, newExStyle); // GWL_EXSTYLE

                // 使样式更改生效
                SetWindowPos(Handle, IntPtr.Zero, 0, 0, 0, 0, 0x0027); // SWP_NOMOVE|SWP_NOSIZE|SWP_NOZORDER|SWP_FRAMECHANGED
            }
            catch
            {
                // 静默处理异常
            }
        }

        #region Windows API

        [System.Runtime.InteropServices.DllImport("user32.dll", SetLastError = true)]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [System.Runtime.InteropServices.DllImport("user32.dll", SetLastError = true)]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);

        [System.Runtime.InteropServices.DllImport("user32.dll", SetLastError = true)]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        #endregion

        #endregion

        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (StyleManager != null) return StyleManager.Style;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        public MetroThemeStyle Theme
        {
            get
            {
                if (StyleManager != null) return StyleManager.Theme;
                return _metroTheme;
            }
            set
            {
                _metroTheme = value;
            }
        }

        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; } = CommonTheme.StyleManager;

        protected override void Dispose(bool disposing)
        {
            if (disposing) RemoveShadow();
            base.Dispose(disposing);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            e.Graphics.SmoothingMode = SmoothingMode.HighQuality;
            e.Graphics.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            e.Graphics.CompositingQuality = CompositingQuality.HighQuality;
            e.Graphics.CompositingMode = CompositingMode.SourceOver;
            e.Graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;

            //e.Graphics.Clear(Color.Transparent);

            DrawGradientBackground(e.Graphics);

            DrawContentAreaBackground(e.Graphics);

            e.Graphics.SetHighQuality();

            if (!string.IsNullOrEmpty(Text))
            {
                var titleSize = new Size(ClientRectangle.Width, Padding.Top);
                var baseFont = CommonString.GetSysNormalFont(23f);
                var titleFont = CommonMethod.ScaleLabelByHeight(Text, baseFont, titleSize);
                if (titleFont.Size > baseFont.Size)
                {
                    titleFont = baseFont;
                }
                var titleHeight = (int)titleFont.GetHeight();
                var availableHeight = Padding.Top; // 蓝色渐变区域高度
                var titleY = Math.Max(8, (availableHeight - titleHeight) / 2); // 垂直居中，最小8px边距
                var titleBounds = new Rectangle(25, titleY, ClientSize.Width - 130, titleHeight);

                TextRenderer.DrawText(e.Graphics, Text, titleFont, titleBounds, MetroPaint.ForeColor.Title(Theme, Style),
                    TextFormatFlags.EndEllipsis | TextFormatFlags.Left | TextFormatFlags.VerticalCenter);
            }

            if (Resizable && (SizeGripStyle == SizeGripStyle.Auto || SizeGripStyle == SizeGripStyle.Show))
                using (var brush2 = new SolidBrush(MetroPaint.ForeColor.Button.Disabled(Theme)))
                {
                    var size = new Size(2, 2);
                    e.Graphics.FillRectangles(brush2, new[]
                    {
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 10, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 10), size),
                        new Rectangle(new Point(ClientRectangle.Width - 14, ClientRectangle.Height - 6), size),
                        new Rectangle(new Point(ClientRectangle.Width - 6, ClientRectangle.Height - 14), size)
                    });
                }
        }

        /// <summary>
        /// 绘制基于主题色的渐变背景
        /// </summary>
        private void DrawGradientBackground(Graphics graphics)
        {
            // 确保绘制区域覆盖整个窗体，包括可能的边框区域
            var fullRect = new Rectangle(0, 0, Width, Height);
            if (fullRect.Width <= 0 || fullRect.Height <= 0) return;

            // 根据主题模式和主题色获取渐变颜色
            Color startColor, endColor;
            var themeColor = MetroPaint.GetStyleColor(Style);
            if (Theme == MetroThemeStyle.Dark)
            {
                // 夜间模式：保持主题色识别度，但整体偏暗
                // 顶部：主题色的深色版本（降低亮度，保持色相）
                startColor = Color.FromArgb(
                    Math.Max(20, Math.Min(80, themeColor.R / 3 + 20)),
                    Math.Max(20, Math.Min(80, themeColor.G / 3 + 20)),
                    Math.Max(20, Math.Min(80, themeColor.B / 3 + 20))
                );

                // 底部：深灰色背景，保持统一的夜间体验
                endColor = MetroPaint.BackColor.Panel.Normal(Theme);
            }
            else
            {
                startColor = themeColor;
                endColor = Color.FromArgb(
                    Math.Max(235, 255 - (255 - themeColor.R) / 8),  // 保持高明度
                    Math.Max(235, 255 - (255 - themeColor.G) / 8),  // 保持高明度
                    Math.Max(235, 255 - (255 - themeColor.B) / 8)   // 保持高明度
                );
            }

            // 使用整个窗体的尺寸绘制渐变，确保完全覆盖
            using (var brush = new LinearGradientBrush(
                fullRect,
                startColor,
                endColor,
                LinearGradientMode.Vertical))
            {
                graphics.FillRectangle(brush, fullRect);
            }
        }

        /// <summary>
        /// 绘制内容区域背景
        /// </summary>
        private void DrawContentAreaBackground(Graphics graphics)
        {
            // 使用与Padding完全一致的边距，内容卡片保持完整大小
            var padding = base.Padding;
            var contentRect = new Rectangle(
                padding.Left,
                padding.Top,
                ClientSize.Width - padding.Left - padding.Right,
                ClientSize.Height - padding.Top - padding.Bottom);

            if (contentRect.Width <= 0 || contentRect.Height <= 0) return;

            Color contentBackColor = GetTransparentFusionBackColor();

            using (var contentBrush = new SolidBrush(contentBackColor))
            {
                // 多层阴影效果，营造自然的浮起感
                // 第一层：较淡的大范围阴影（只绘制最外圈环形）
                using (var shadowBrush1 = new SolidBrush(Color.FromArgb(6, 0, 0, 0)))
                {
                    var shadow1OuterRect = new Rectangle(contentRect.X + 2, contentRect.Y + 3, contentRect.Width, contentRect.Height);
                    var shadow1InnerRect = new Rectangle(contentRect.X + 3, contentRect.Y + 4, contentRect.Width - 2, contentRect.Height - 2);

                    // 创建环形区域：外矩形减去内矩形
                    using (var shadow1Region = new Region())
                    {
                        using (var outerPath = CreateRoundedRectanglePath(shadow1OuterRect, 10))
                        using (var innerPath = CreateRoundedRectanglePath(shadow1InnerRect, 10))
                        {
                            shadow1Region.Union(outerPath);
                            shadow1Region.Exclude(innerPath);
                            graphics.FillRegion(shadowBrush1, shadow1Region);
                        }
                    }
                }

                // 第二层：较深的小范围阴影（只绘制中间圈环形）
                using (var shadowBrush2 = new SolidBrush(Color.FromArgb(12, 0, 0, 0)))
                {
                    var shadow2OuterRect = new Rectangle(contentRect.X + 1, contentRect.Y + 1, contentRect.Width, contentRect.Height);
                    var shadow2InnerRect = new Rectangle(contentRect.X + 2, contentRect.Y + 2, contentRect.Width - 2, contentRect.Height - 2);

                    // 创建环形区域：外矩形减去内矩形
                    using (var shadow2Region = new Region())
                    {
                        using (var outerPath = CreateRoundedRectanglePath(shadow2OuterRect, 10))
                        using (var innerPath = CreateRoundedRectanglePath(shadow2InnerRect, 10))
                        {
                            shadow2Region.Union(outerPath);
                            shadow2Region.Exclude(innerPath);
                            graphics.FillRegion(shadowBrush2, shadow2Region);
                        }
                    }
                }

                // 纯白色内容区域，圆角调整为10px，更现代的感觉
                // 分层绘制：先绘制圆角边框，再绘制中心矩形
                var cornerRadius = 10;
                var borderWidth = cornerRadius; // 圆角边框宽度

                // 计算中心矩形区域
                var centerRect = new Rectangle(
                    contentRect.X + borderWidth,
                    contentRect.Y + borderWidth,
                    contentRect.Width - 2 * borderWidth,
                    contentRect.Height - 2 * borderWidth);

                // 第一步：绘制圆角边框（挖掉中间矩形）
                using (var path = CreateRoundedRectanglePath(contentRect, cornerRadius))
                {
                    // 设置裁剪区域，排除中间矩形
                    var originalClip = graphics.Clip;
                    if (centerRect.Width > 0 && centerRect.Height > 0)
                    {
                        using (var clipRegion = new Region(contentRect))
                        {
                            clipRegion.Exclude(centerRect);
                            graphics.Clip = clipRegion;
                        }
                    }
                    // 绘制完整的圆角路径，但只会绘制边框部分
                    graphics.FillPath(contentBrush, path);
                    graphics.Clip = originalClip;
                }

                // 第二步：低质量绘制中心矩形
                if (centerRect.Width > 0 && centerRect.Height > 0)
                {
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.PixelOffsetMode = PixelOffsetMode.Default;
                    graphics.CompositingQuality = CompositingQuality.Default;
                    graphics.TextRenderingHint = TextRenderingHint.SystemDefault;
                    graphics.InterpolationMode = InterpolationMode.Default;
                    graphics.FillRectangle(contentBrush, centerRect);
                }
            }
        }

        /// <summary>
        /// 获取透明融合方案的内容区域背景色
        /// </summary>
        private Color GetTransparentFusionBackColor()
        {
            if (Theme == MetroThemeStyle.Dark)
            {
                // 夜间模式：使用半透明的深色，让渐变背景透过来
                // 透明度设置为85%，既保持可读性又能看到渐变效果
                var baseColor = MetroPaint.BackColor.Form(Theme);
                return Color.FromArgb(217, baseColor.R, baseColor.G, baseColor.B); // 85% 不透明度
            }
            else
            {
                // 日间模式：使用半透明的浅色，让渐变背景透过来
                // 透明度设置为90%，保持良好的文字对比度
                var baseColor = MetroPaint.BackColor.Form(Theme);
                return Color.FromArgb(230, baseColor.R, baseColor.G, baseColor.B); // 90% 不透明度
            }
        }

        #region 全局样式管理 - 无感主题适配

        /// <summary>
        /// 递归应用主题样式到所有子控件（无感适配）
        /// </summary>
        protected virtual void ApplyThemeToAllControls(Control parent)
        {
            if (parent == null) return;

            foreach (Control control in parent.Controls)
            {
                ApplyControlTheme(control);

                // 递归处理子控件
                if (control.HasChildren)
                {
                    ApplyThemeToAllControls(control);
                }
            }
        }

        /// <summary>
        /// 智能识别控件类型并应用相应的主题样式
        /// </summary>
        protected virtual void ApplyControlTheme(Control control)
        {
            if (control == null) return;
            var controlTypeName = control.GetType().Name;
            if (controlTypeName.Contains("YButton"))
            {
                return; // 直接跳过，不做任何处理
            }
            try
            {
                switch (control)
                {
                    case ComboBox comboBox:
                    case ToolStrip strip:
                        break;

                    case TabPage tabPage:
                    case TabControl tabControl:
                        try
                        {
                            CommonMethod.SetStyle(control, ControlStyles.SupportsTransparentBackColor, true);
                            control.BackColor = GetTransparentFusionBackColor();
                        }
                        catch { }
                        control.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
                        break;

                    case LinkLabel linkLabel:
                        linkLabel.LinkColor = MetroPaint.ForeColor.Link.Normal(Theme);
                        linkLabel.VisitedLinkColor = MetroPaint.ForeColor.Link.Normal(Theme);
                        break;

                    case Label label:
                        if (label.AccessibleDescription?.Contains("预览\r\n") == true)
                        {

                        }
                        else
                        {
                            label.BackColor = Color.Transparent;
                            ApplyLabelTheme(label);
                        }
                        break;

                    case TextBox textBox:
                        textBox.BackColor = MetroPaint.BackColor.Input.Normal(Theme);
                        textBox.ForeColor = MetroPaint.ForeColor.InputText.Normal(Theme);
                        break;

                    case MenuButton menuButton:
                        control.BackColor = Color.Transparent;
                        control.ForeColor = MetroPaint.ForeColor.TextBox.Normal(Theme);
                        break;

                    case Panel panel:
                    case RadioButton radioButton:
                    case CheckBox checkBox:
                    case GroupBox groupBox:
                        control.BackColor = Color.Transparent;
                        control.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
                        break;

                    case NumericUpDown numericUpDown:
                        numericUpDown.BackColor = MetroPaint.BackColor.Input.Normal(Theme);
                        numericUpDown.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
                        break;
                    case SkinButton skinButton:
                        skinButton.BaseColor = MetroPaint.BackColor.Input.Normal(Theme);
                        skinButton.ForeColorSuit = true;
                        break;
                    case Button button when !(button is Controls.MetroButton):
                        if (!_windowButtonList.Any(p => p.Value.Contains(button)))
                        {
                            control.BackColor = Color.Transparent;
                            control.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
                        }
                        break;
                    default:
                        try
                        {
                            CommonMethod.SetStyle(control, ControlStyles.SupportsTransparentBackColor, true);
                            control.BackColor = Color.Transparent;
                            control.ForeColor = MetroPaint.ForeColor.Label.Normal(Theme);
                        }
                        catch { }
                        break;
                }
            }
            catch
            {
                // 忽略样式应用过程中的异常，确保不影响现有功能
            }
        }

        /// <summary>
        /// 智能应用标签主题（根据内容和命名判断用途）
        /// </summary>
        private void ApplyLabelTheme(Label label)
        {
            // 如果是默认的深灰色或黑色，说明是标题标签
            if (label.ForeColor == Color.FromArgb(50, 50, 50) ||
                label.ForeColor == Color.Black ||
                label.ForeColor == SystemColors.ControlText)
            {
                // 对于重要标签，使用更亮的 MetroFramework 标准颜色
                label.ForeColor = MetroPaint.ForeColor.Button.Normal(Theme);
            }
            // 如果是蓝色，说明是数据显示标签
            else if (label.ForeColor == Color.DodgerBlue ||
                     label.ForeColor == Color.Blue ||
                     label.ForeColor == Color.FromArgb(0, 0, 64)) // FormUpdate 中的深蓝色
            {
                label.ForeColor = MetroPaint.ForeColor.Data.Normal(Theme);
            }
            // 如果是红色，说明是警告/错误标签
            else if (label.ForeColor == Color.Red)
            {
                label.ForeColor = MetroPaint.ForeColor.Warning.Normal(Theme);
            }
            // 如果是灰色，说明是次要信息标签
            else if (label.ForeColor == Color.DimGray ||
                     label.ForeColor == Color.Gray)
            {
                label.ForeColor = MetroPaint.ForeColor.Secondary.Normal(Theme);
            }
            else
            {
                label.ForeColor = MetroPaint.ForeColor.InputText.Normal(Theme);
            }
        }

        #endregion

        /// <summary>
        /// 重写 DisplayRectangle，让子控件的布局区域在内容卡片内部，提供合适的内边距
        /// 这样 Dock.Fill 等布局会在卡片内部，不会覆盖边框和阴影，并有良好的视觉呼吸感
        /// </summary>
        public override Rectangle DisplayRectangle
        {
            get
            {
                var padding = base.Padding;
                // 内容卡片的内边距：考虑阴影(3px) + 圆角(10px) + 视觉舒适度 = 12px
                const int contentPadding = 12;

                return new Rectangle(
                    padding.Left + contentPadding,
                    padding.Top + contentPadding,
                    Math.Max(0, ClientSize.Width - padding.Left - padding.Right - contentPadding * 2),
                    Math.Max(0, ClientSize.Height - padding.Top - padding.Bottom - contentPadding * 2)
                );
            }
        }

        /// <summary>
        /// 创建圆角矩形路径
        /// </summary>
        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        protected override void OnClosed(EventArgs e)
        {
            if (Owner != null) Owner = null;
            // 彻底移除阴影窗体
            RemoveShadow();
            // 调用基类方法
            base.OnClosed(e);
            // 释放定时器资源
            if (displayChangeTimer != null)
            {
                displayChangeTimer.Dispose();
                displayChangeTimer = null;
            }
        }

        public AutoSizeFormClass InitForm { get; set; } = new AutoSizeFormClass();

        private bool IsInitFinish = false;

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            if (DesignMode) return;

            this.Width += 48;
            this.Height += 48;

            if (ControlBox)
            {
                AddWindowButton(WindowButtons.Close);
                if (MaximizeBox) AddWindowButton(WindowButtons.Maximize);
                if (MinimizeBox) AddWindowButton(WindowButtons.Minimize);
            }

            // 窗体显示后再记录控件初始位置
            InitForm.controllInitializeSize(this);

            // 然后进行DPI调整
            HighDpiHelper.AdjustControlImagesDpiScale(this);

            switch (StartPosition)
            {
                case FormStartPosition.CenterParent:
                    CenterToParent();
                    break;
                case FormStartPosition.CenterScreen:
                    if (IsMdiChild)
                        CenterToParent();
                    else
                        CenterToScreen();
                    break;
            }

            CreateShadow();

            // 在窗体完全显示并调整后标记初始化完成
            IsInitFinish = true;
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            OnThemeChange();

            UpdateWindowButtonPosition();

            if (AutoRemoveCompositedStyle)
            {
                RemoveCompositedStyle();
            }

            this.AutoSizeMutilScreen();

            // 添加事件处理
            HighDpiHelper.AttachMouseMoveEvent(this);
        }

        protected override void OnActivated(EventArgs e)
        {
            base.OnActivated(e);
            if (IsInitFinish)
                this.RefreshDpiScale();

            // 处理AeroShadow类型的阴影
            if (_shadowType == MetroFormShadowType.AeroShadow && IsAeroThemeEnabled() && IsDropShadowSupported())
            {
                var attrValue = 2;
                DwmApi.DwmSetWindowAttribute(Handle, 2, ref attrValue, 4);
                var mArgins = new DwmApi.Margins
                {
                    cyBottomHeight = 1,
                    cxLeftWidth = 0,
                    cxRightWidth = 0,
                    cyTopHeight = 0
                };

                DwmApi.DwmExtendFrameIntoClientArea(Handle, ref mArgins);
            }

            // 确保阴影窗体的可见性状态与当前窗体状态一致
            if (_shadowForm != null && !_shadowForm.IsDisposed)
            {
                bool shouldBeVisible = WindowState != FormWindowState.Maximized && Visible && Opacity > 0 && ShadowType != MetroFormShadowType.None;
                if (_shadowForm.Visible != shouldBeVisible)
                {
                    _shadowForm.Visible = shouldBeVisible;
                }
            }

            // 处理TopMost属性
            if (Owner != null && Owner.TopMost && !TopMost)
            {
                this.TopMost = Owner.TopMost;
            }
        }

        protected override void OnResizeEnd(EventArgs e)
        {
            base.OnResizeEnd(e);
            UpdateWindowButtonPosition();
        }

        private System.Threading.Timer displayChangeTimer;

        protected override void WndProc(ref Message m)
        {
            if (DesignMode)
            {
                base.WndProc(ref m);
                return;
            }

            switch (m.Msg)
            {
                case (int)Messages.WM_DISPLAYCHANGE:
                    displayChangeTimer = new System.Threading.Timer(
                         _ =>
                        {
                            CommonMethod.DetermineCall(this, () =>
                            {
                                this.RefreshDpiScale(true);
                            });
                        },
                        null,
                        new TimeSpan(0, 0, 3),
                        Timeout.InfiniteTimeSpan
                    );
                    break;
                case (int)Messages.WM_SYSCOMMAND:
                    int sc = m.WParam.ToInt32() & 0xFFF0;
                    switch (sc)
                    {
                        case (int)Messages.SC_MOVE:
                            if (!Movable) return;
                            break;

                        case (int)Messages.SC_MAXIMIZE:
                            break;
                        case (int)Messages.SC_RESTORE:
                            break;
                    }
                    break;

                case (int)Messages.WM_NCLBUTTONDBLCLK:
                case (int)Messages.WM_LBUTTONDBLCLK:
                    if (!MaximizeBox) return;
                    break;
                case (int)Messages.WM_NCHITTEST:
                    WinApi.HitTest ht = HitTestNca(m.HWnd, m.WParam, m.LParam);
                    if (ht != WinApi.HitTest.HTCLIENT)
                    {
                        m.Result = (IntPtr)ht;
                        return;
                    }
                    break;
                case (int)Messages.WM_DWMCOMPOSITIONCHANGED:
                    break;
            }

            base.WndProc(ref m);
            switch (m.Msg)
            {
                case (int)Messages.WM_GETMINMAXINFO:
                    OnGetMinMaxInfo(m.HWnd, m.LParam);
                    break;
                case (int)Messages.WM_SIZE:
                    {
                        if (_shadowForm != null && !_shadowForm.IsDisposed)
                        {
                            bool shouldBeVisible = WindowState != FormWindowState.Maximized && Visible && Opacity > 0 && ShadowType != MetroFormShadowType.None;
                            if (_shadowForm.Visible != shouldBeVisible)
                            {
                                _shadowForm.Visible = shouldBeVisible;
                            }
                        }
                        _windowButtonList.TryGetValue(WindowButtons.Maximize, out var value);
                        if (value == null || value.Count <= 0) break;
                        value.FirstOrDefault().BackgroundImage = null;
                        break;
                    }
            }
        }

        [SecuritySafeCritical]
        private unsafe void OnGetMinMaxInfo(IntPtr hwnd, IntPtr lParam)
        {
            var ptr = (WinApi.MINMAXINFO*)(void*)lParam;
            var screen = Screen.FromHandle(hwnd);
            ptr->ptMaxSize.x = screen.WorkingArea.Width;
            ptr->ptMaxSize.y = screen.WorkingArea.Height;
            ptr->ptMaxPosition.x = Math.Abs(screen.WorkingArea.Left - screen.Bounds.Left);
            ptr->ptMaxPosition.y = Math.Abs(screen.WorkingArea.Top - screen.Bounds.Top);
        }

        private WinApi.HitTest HitTestNca(IntPtr hwnd, IntPtr wparam, IntPtr lparam)
        {
            Point vPoint = new Point((short)lparam, (short)((int)lparam >> 16));
            int vPadding = Math.Max(Padding.Right, Padding.Bottom);

            if (Resizable)
            {
                if (RectangleToScreen(new Rectangle(ClientRectangle.Width - vPadding, ClientRectangle.Height - vPadding, vPadding, vPadding)).Contains(vPoint))
                    return WinApi.HitTest.HTBOTTOMRIGHT;
            }

            if (RectangleToScreen(new Rectangle(borderWidth, borderWidth, ClientRectangle.Width - 2 * borderWidth, 50)).Contains(vPoint))
                return WinApi.HitTest.HTCAPTION;

            return WinApi.HitTest.HTCLIENT;
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            if (e.Button == MouseButtons.Left && Movable && WindowState != FormWindowState.Maximized &&
                Width - borderWidth > e.Location.X && e.Location.X > borderWidth && e.Location.Y > borderWidth) MoveControl();
        }

        [SecuritySafeCritical]
        private void MoveControl()
        {
            WinApi.ReleaseCapture();
            WinApi.SendMessage(Handle, (int)Messages.WM_NCLBUTTONDOWN, (int)WinApi.HitTest.HTCAPTION, 0);
        }

        [SecuritySafeCritical]
        private bool IsAeroThemeEnabled()
        {
            if (Environment.OSVersion.Version.Major <= 5) return false;
            DwmApi.DwmIsCompositionEnabled(out var pfEnabled);
            return pfEnabled;
        }

        private bool IsDropShadowSupported()
        {
            return Environment.OSVersion.Version.Major > 5 && SystemInformation.IsDropShadowEnabled;
        }

        /// <summary>
        /// 统一的按钮创建方法
        /// </summary>
        private Button CreateButton(WindowButtons buttonType, Size? size = null, Image image = null,
            EventHandler click = null, string text = null, float fontSize = 0, bool canInverse = true)
        {
            bool isCustomButton = buttonType == WindowButtons.Custom;

            var button = new Button
            {
                FlatStyle = FlatStyle.Flat,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                TabStop = false,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false,
                Visible = false,
                Location = new Point(-100, -100),
                BackColor = Color.Transparent,
                Tag = buttonType,
                Text = text,
                TextImageRelation = isCustomButton ? TextImageRelation.ImageBeforeText : TextImageRelation.Overlay,
                ImageAlign = ContentAlignment.MiddleCenter,
                TextAlign = ContentAlignment.MiddleRight,
                Image = image,
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                ForeColor = MetroPaint.ForeColor.Title(Theme, Style)
            };

            button.FlatAppearance.BorderSize = 0;
            // 使用主题适配的半透明白色悬停效果
            var hoverAlpha = Theme == MetroThemeStyle.Dark ? 20 : 30; // 夜间模式使用更低的透明度
            var pressAlpha = Theme == MetroThemeStyle.Dark ? 30 : 40; // 按下时稍微更明显
            button.FlatAppearance.MouseDownBackColor = Color.FromArgb(pressAlpha, 255, 255, 255);
            button.FlatAppearance.MouseOverBackColor = Color.FromArgb(hoverAlpha, 255, 255, 255);

            CommonMethod.SetStyle(button, ControlStyles.SupportsTransparentBackColor, true);
            CommonMethod.SetStyle(button, ControlStyles.Selectable, false);

            if (fontSize > 0)
            {
                button.Font = CommonString.GetSysNormalFont(fontSize);
            }
            if (size.HasValue)
            {
                button.Size = size.Value;
                button.AutoSize = false;
            }
            else
            {
                button.AutoSize = true;
                button.Size = new Size(1, 1);
            }

            if (!isCustomButton)
            {
                button.TextChanged += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(button.Text))
                    {
                        button.Text = string.Empty;
                    }
                };

                button.AccessibleDescription = canInverse.ToString();
            }

            if (click != null)
            {
                button.Click += click;
            }

            AddToButtonList(buttonType, button);
            return button;
        }

        /// <summary>
        /// 将控件添加到按钮列表
        /// </summary>
        private void AddToButtonList(WindowButtons buttonType, Control control)
        {
            Controls.Add(control);
            if (_windowButtonList.ContainsKey(buttonType))
            {
                _windowButtonList[buttonType].Add(control);
            }
            else
            {
                _windowButtonList.Add(buttonType, new List<Control> { control });
            }
        }

        internal Control AddWindowButton(WindowButtons button, EventHandler click = null, bool canInverse = true, Image image = null)
        {
            var size = button == WindowButtons.Top ? new Size(30, 25) : new Size(25, 25);
            var windowButton = CreateButton(button, size, image, click ?? WindowButton_Click, null, 0, canInverse);

            if (image == null)
            {
                SetWindowButtonImage(windowButton);
            }

            return windowButton;
        }

        internal Button AddCustomButton(string text, Image image, float fontSize, EventHandler click = null, string foreColor = "DarkGray")
        {
            var customButton = CreateButton(WindowButtons.Custom, null, image, click, text, fontSize);
            customButton.TextChanged += (obj, erg) => UpdateWindowButtonPosition();
            return customButton;
        }

        private void WindowButton_Click(object sender, EventArgs e)
        {
            var windowButton = sender as Button;
            if (windowButton == null) return;
            switch ((WindowButtons)windowButton.Tag)
            {
                case WindowButtons.Close:
                    Close();
                    break;
                case WindowButtons.Minimize:
                    WindowState = FormWindowState.Minimized;
                    break;
                case WindowButtons.Maximize:
                    windowButton.Image = null;
                    WindowState = WindowState == FormWindowState.Normal ? FormWindowState.Maximized : FormWindowState.Normal;
                    UpdateWindowButtonPosition();
                    break;
            }
        }

        public Rectangle LeftButtonRectangle { get; set; }

        internal void UpdateWindowButtonImage()
        {
            foreach (var item in _windowButtonList)
            {
                if (item.Key.Equals(WindowButtons.Custom))
                {
                    continue;
                }
                item.Value.ForEach(p =>
                {
                    if (p is Button btn)
                    {
                        btn.Image = null;
                    }
                });
            }
        }

        internal void UpdateWindowButtonPosition()
        {
            // 暂停布局逻辑，防止多次重绘
            SuspendLayout();

            var firstButtonLocation = new Point(ClientRectangle.Width - 15, 10); // 参考效果图：右边距15px，顶部边距10px
            var lastDrawedButtonPosition = firstButtonLocation.X;

            // 先将所有按钮设为不可见，避免闪烁
            foreach (var item in _windowButtonList)
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.Visible = false;
                }
            }

            // 计算所有按钮的位置，保持原有的排序
            foreach (var item in _windowButtonList.OrderBy(p => p.Key.GetHashCode()))
            {
                foreach (var ctrl in item.Value)
                {
                    if (ctrl is Button btn)
                    {
                        if (btn.AutoSize)
                        {
                            btn.Size = new Size();
                        }
                        if (item.Key != WindowButtons.Custom)
                        {
                            SetWindowButtonImage(btn);
                        }
                    }

                    // 设置位置 - 参考效果图调整
                    ctrl.Location = new Point(lastDrawedButtonPosition - ctrl.Width, 10); // 使用统一的顶部边距10px
                    if (item.Key == WindowButtons.Menu || item.Key == WindowButtons.Dark)
                    {
                        ctrl.Top += 2; // 微调小图标的位置
                    }
                    else if (item.Key == WindowButtons.Top)
                    {
                        ctrl.Top += 3; // 微调置顶按钮位置
                    }
                    lastDrawedButtonPosition -= ctrl.Width + 5; // 参考效果图增加按钮间距到5px
                    LeftButtonRectangle = ctrl.Bounds;
                }
            }

            // 所有位置计算完毕后，一次性显示所有按钮并置于前景
            foreach (var item in _windowButtonList)
            {
                foreach (var ctrl in item.Value)
                {
                    ctrl.BringToFront();
                    ctrl.Visible = true;
                }
            }

            // 恢复布局逻辑并刷新
            ResumeLayout();
            Refresh();
        }

        private void CreateShadow()
        {
            // 确保在创建新阴影前先移除旧阴影
            RemoveShadow();

            // 根据阴影类型创建对应的阴影窗体
            switch (ShadowType)
            {
                case MetroFormShadowType.None:
                    break;
                case MetroFormShadowType.Flat:
                    _shadowForm = new MetroFlatDropShadow(this);
                    break;
                case MetroFormShadowType.DropShadow:
                    _shadowForm = new MetroRealisticDropShadow(this);
                    break;
            }

            // 设置阴影窗体的可见性
            if (_shadowForm != null)
            {
                // 减少不必要的重绘，提高性能
                _shadowForm.Visible = WindowState != FormWindowState.Maximized && Visible && Opacity > 0;
            }
        }

        private void RemoveShadow()
        {
            // 快速检查阴影窗体是否存在或已释放
            if (_shadowForm == null || _shadowForm.IsDisposed) return;

            try
            {
                // 隐藏阴影窗体
                _shadowForm.Visible = false;

                // 简化所有权关系处理
                // 直接断开阴影窗体与当前窗体的所有权关系
                if (_shadowForm.Owner == this)
                {
                    _shadowForm.Owner = null;
                }

                // 彻底释放资源
                _shadowForm.Dispose();
            }
            catch (Exception)
            {
                // 忽略释放过程中的异常
            }
            finally
            {
                // 确保引用被清除
                _shadowForm = null;
            }
        }

        internal enum WindowButtons
        {
            Close = 0,
            Maximize = 1,
            Minimize = 2,
            Menu = 3,
            Top = 4,
            Dark = 5,
            Plugin = 6,
            Custom = 7,
        }

        /// <summary>
        /// 获取窗口按钮图标
        /// </summary>
        /// <param name="button">按钮类型</param>
        /// <param name="canInverse">是否可反色</param>
        /// <param name="accessibleDescription">插件按钮的图标描述</param>
        /// <returns>按钮图标</returns>
        internal Bitmap GetWindowButtonImage(WindowButtons button, bool canInverse = true, string accessibleDescription = null)
        {
            Bitmap image = null;
            switch (button)
            {
                case WindowButtons.Close:
                    image = DrawCloseIcon();
                    break;
                case WindowButtons.Minimize:
                    image = DrawMinimizeIcon();
                    break;
                case WindowButtons.Maximize:
                    image = WindowState == FormWindowState.Normal ? DrawMaximizeIcon() : DrawRestoreIcon();
                    break;
                case WindowButtons.Menu:
                    image = DrawMenuIcon();
                    break;
                case WindowButtons.Top:
                    var topMost = FindForm().TopMost;
                    image = CommonSetting.夜间模式 ? (topMost ? Resources.top_dark : Resources.untop_dark) : (topMost ? Resources.top : Resources.untop);
                    break;
                case WindowButtons.Dark:
                    image = CommonSetting.夜间模式 ? Resources.sun : Resources.sun_dark;
                    break;
                case WindowButtons.Plugin:
                    image = ImageProcessHelper.GetImageByBase64AndReverse(accessibleDescription, canInverse);
                    break;
            }
            image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
            return image;
        }

        /// <summary>
        /// 绘制关闭图标（X）
        /// </summary>
        private Bitmap DrawCloseIcon()
        {
            var size = 20; // 统一增大尺寸
            var bitmap = new Bitmap(size, size);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                using (var pen = new Pen(MetroPaint.ForeColor.Title(Theme, Style), 2.0f))
                {
                    // 绘制 X 形状，调整边距使图标更居中
                    var margin = 5f;
                    g.DrawLine(pen, margin, margin, size - margin, size - margin);
                    g.DrawLine(pen, size - margin, margin, margin, size - margin);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// 绘制最小化图标（一条横线）
        /// </summary>
        private Bitmap DrawMinimizeIcon()
        {
            var size = 20;
            var bitmap = new Bitmap(size, size);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                using (var pen = new Pen(MetroPaint.ForeColor.Title(Theme, Style), 2.0f))
                {
                    // 绘制底部横线，调整位置使其更居中
                    var y = size / 2;
                    g.DrawLine(pen, 4f, y, size - 4f, y);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// 绘制最大化图标（方框）
        /// </summary>
        private Bitmap DrawMaximizeIcon()
        {
            var size = 20;
            var bitmap = new Bitmap(size, size);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                using (var pen = new Pen(MetroPaint.ForeColor.Title(Theme, Style), 2.0f))
                {
                    // 绘制方框，调整边距
                    var margin = 5f;
                    var rect = new RectangleF(margin, margin, size - margin * 2, size - margin * 2);
                    g.DrawRectangle(pen, rect.X, rect.Y, rect.Width, rect.Height);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// 绘制还原图标（两个重叠的方框）
        /// </summary>
        private Bitmap DrawRestoreIcon()
        {
            var size = 20;
            var bitmap = new Bitmap(size, size);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                using (var pen = new Pen(MetroPaint.ForeColor.Title(Theme, Style), 2.0f))
                {
                    // 绘制后面的方框（右上）
                    var rect1 = new RectangleF(7f, 3f, 10f, 10f);
                    g.DrawRectangle(pen, rect1.X, rect1.Y, rect1.Width, rect1.Height);

                    // 绘制前面的方框（左下）
                    var rect2 = new RectangleF(3f, 7f, 10f, 10f);
                    g.DrawRectangle(pen, rect2.X, rect2.Y, rect2.Width, rect2.Height);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// 绘制菜单图标（三条横线）
        /// </summary>
        private Bitmap DrawMenuIcon()
        {
            var size = 20;
            var bitmap = new Bitmap(size, size);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                using (var pen = new Pen(MetroPaint.ForeColor.Title(Theme, Style), 2.0f))
                {
                    // 绘制三条横线，模拟汉堡菜单
                    var lineWidth = 14f;
                    var startX = (size - lineWidth) / 2;
                    var spacing = 5f;

                    // 第一条线
                    var y1 = 5f;
                    g.DrawLine(pen, startX, y1, startX + lineWidth, y1);

                    // 第二条线
                    var y2 = y1 + spacing;
                    g.DrawLine(pen, startX, y2, startX + lineWidth, y2);

                    // 第三条线
                    var y3 = y2 + spacing;
                    g.DrawLine(pen, startX, y3, startX + lineWidth, y3);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// 设置窗口按钮图标
        /// </summary>
        /// <param name="button">按钮控件</param>
        internal void SetWindowButtonImage(Button button)
        {
            if (button?.Tag is WindowButtons buttonType)
            {
                var canInverse = bool.TryParse(button.AccessibleDescription, out var result) ? result : true;
                var accessibleDescription = button.AccessibleDefaultActionDescription;
                button.Image = GetWindowButtonImage(buttonType, canInverse, accessibleDescription);
            }
        }

        protected const int WS_EX_TRANSPARENT = 0x20;
        protected const int WS_EX_LAYERED = 0x80000;
        protected const int WS_EX_NOACTIVATE = 0x8000000;

        protected abstract class MetroShadowBase : Form
        {
            private readonly int _shadowSize;

            private readonly int _wsExStyle;

            //private bool _isBringingToFront;

            private long _lastResizedOn;

            // 阴影绘制状态标志，防止并发绘制导致阴影叠加
            private volatile bool _isPainting = false;

            protected MetroShadowBase(MetroForm targetForm, int shadowSize, int wsExStyle)
            {
                TargetForm = targetForm;
                _shadowSize = shadowSize;
                _wsExStyle = wsExStyle;
                TargetForm.Activated += OnTargetFormActivated;
                //TargetForm.Activated += TargetForm_LostFocus;
                //TargetForm.LostFocus += TargetForm_LostFocus;
                TargetForm.ResizeBegin += OnTargetFormResizeBegin;
                TargetForm.ResizeEnd += OnTargetFormResizeEnd;
                TargetForm.VisibleChanged += OnTargetFormVisibleChanged;
                TargetForm.SizeChanged += OnTargetFormSizeChanged;
                TargetForm.Move += OnTargetFormMove;
                TargetForm.Resize += OnTargetFormResize;
                TargetForm.Shown += TargetForm_Shown;

                if (TargetForm.Owner != null) Owner = TargetForm.Owner;
                TargetForm.Owner = this;
                MaximizeBox = false;
                MinimizeBox = false;
                ShowInTaskbar = false;
                ShowIcon = false;
                FormBorderStyle = FormBorderStyle.None;
                Bounds = GetShadowBounds();
            }

            protected MetroForm TargetForm { get; }

            protected override CreateParams CreateParams
            {
                get
                {
                    //const int WS_EX_NOACTIVATE = 0x08000000;
                    //const int WS_CHILD = 0x40000000;
                    //CreateParams cp = base.CreateParams;
                    //cp.Style |= WS_CHILD;
                    //cp.ExStyle |= WS_EX_NOACTIVATE;
                    //return cp;
                    var createParams = base.CreateParams;
                    //createParams.Style |= 0x40000000;
                    createParams.ExStyle |= _wsExStyle;
                    //createParams.ExStyle |= 0x08000000;
                    return createParams;
                }
            }

            private bool IsResizing => _lastResizedOn > 0;

            private Rectangle GetShadowBounds()
            {
                var bounds = TargetForm.Bounds;
                bounds.Inflate(_shadowSize, _shadowSize);
                return bounds;
            }

            protected abstract void PaintShadow();

            protected abstract void ClearShadow();

            private bool _isTargetFormShown;

            private void OnTargetFormTopMostChanged(object sender, EventArgs e)
            {
                TopMost = TargetForm.TopMost;
            }

            private void TargetForm_Shown(object sender, EventArgs e)
            {
                OnTargetFormTopMostChanged(sender, e);
                _isTargetFormShown = true;

                // 使用异步延迟方式绘制阴影，确保主窗体已完全绘制
                BeginInvoke(new Action(() =>
                {
                    PaintShadowIfVisible();
                    if (Visible)
                        TargetForm?.ForceActivate();
                }));
            }

            private void OnTargetFormActivated(object sender, EventArgs e)
            {
                if (Visible) Update();
            }

            private void OnTargetFormVisibleChanged(object sender, EventArgs e)
            {
                Visible = IsCanShow;
                Update();
            }

            private void OnTargetFormMove(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
            }

            private void OnTargetFormResizeBegin(object sender, EventArgs e)
            {
                _lastResizedOn = ServerTime.DateTime.Ticks;
            }

            private void OnTargetFormResizeEnd(object sender, EventArgs e)
            {
                _lastResizedOn = 0L;
                PaintShadowIfVisible();
            }

            private void OnTargetFormResize(object sender, EventArgs e)
            {
                ClearShadow();
            }

            private void OnTargetFormSizeChanged(object sender, EventArgs e)
            {
                Bounds = GetShadowBounds();
                if (!IsResizing) PaintShadowIfVisible();
            }

            private void PaintShadowIfVisible()
            {
                Visible = IsCanShow;
                if (Visible && !_isPainting)
                {
                    _isPainting = true;
                    try
                    {
                        PaintShadow();
                    }
                    finally
                    {
                        _isPainting = false;
                    }
                }
            }

            public bool IsCanShow => _isTargetFormShown && TargetForm != null && TargetForm.Visible && TargetForm.WindowState == FormWindowState.Normal && TargetForm.Opacity > 0;
        }

        protected class MetroFlatDropShadow : MetroShadowBase
        {
            private Point _offset = new Point(-6, -6);

            public MetroFlatDropShadow(MetroForm targetForm)
                : base(targetForm, 6, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var pblend = default(WinApi.BLENDFUNCTION);
                    pblend.BlendOp = 0;
                    pblend.BlendFlags = 0;
                    pblend.SourceConstantAlpha = opacity;
                    pblend.AlphaFormat = 1;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(Color.Black,
                    new Rectangle(0, 0, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(Color color, Rectangle shadowCanvasArea)
            {
                var rect = shadowCanvasArea;
                var rect2 = new Rectangle(shadowCanvasArea.X + (-_offset.X - 1), shadowCanvasArea.Y + (-_offset.Y - 1),
                    shadowCanvasArea.Width - (-_offset.X * 2 - 1), shadowCanvasArea.Height - (-_offset.Y * 2 - 1));
                var bitmap = new Bitmap(rect.Width, rect.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                using (Brush brush = new SolidBrush(Color.FromArgb(30, Color.Black)))
                {
                    graphics.FillRectangle(brush, rect);
                }

                using (Brush brush2 = new SolidBrush(Color.FromArgb(60, Color.Black)))
                {
                    graphics.FillRectangle(brush2, rect2);
                }

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }
        }

        protected class MetroRealisticDropShadow : MetroShadowBase
        {
            public MetroRealisticDropShadow(MetroForm targetForm)
                : base(targetForm, 15, WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_NOACTIVATE)
            {
            }

            protected override void OnLoad(EventArgs e)
            {
                base.OnLoad(e);
                OnPaint(null);
            }

            protected override void OnPaint(PaintEventArgs e)
            {
                Visible = IsCanShow;
                if (Visible)
                    PaintShadow();
            }

            protected override void PaintShadow()
            {
                using (var bitmap = DrawBlurBorder())
                {
                    SetBitmap(bitmap, byte.MaxValue);
                }
            }

            protected override void ClearShadow()
            {
                var bitmap = new Bitmap(Width, Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.Clear(Color.Transparent);
                graphics.Flush();
                graphics.Dispose();
                SetBitmap(bitmap, byte.MaxValue);
                bitmap.Dispose();
            }

            [SecuritySafeCritical]
            private void SetBitmap(Bitmap bitmap, byte opacity)
            {
                if (bitmap.PixelFormat != PixelFormat.Format32bppArgb)
                    throw new ApplicationException("The bitmap must be 32ppp with alpha-channel.");

                if (TargetForm == null || TargetForm.IsDisposed)
                {
                    return;
                }
                var dC = WinApi.GetDC(IntPtr.Zero);
                var intPtr = WinApi.CreateCompatibleDC(dC);
                var intPtr2 = IntPtr.Zero;
                var hObject = IntPtr.Zero;
                try
                {
                    intPtr2 = bitmap.GetHbitmap(Color.FromArgb(0));
                    hObject = WinApi.SelectObject(intPtr, intPtr2);
                    var psize = new WinApi.SIZE(bitmap.Width, bitmap.Height);
                    var pprSrc = new WinApi.POINT(0, 0);
                    var pptDst = new WinApi.POINT(Left, Top);
                    var bLendfunction = default(WinApi.BLENDFUNCTION);
                    bLendfunction.BlendOp = 0;
                    bLendfunction.BlendFlags = 0;
                    bLendfunction.SourceConstantAlpha = opacity;
                    bLendfunction.AlphaFormat = 1;
                    var pblend = bLendfunction;
                    WinApi.UpdateLayeredWindow(Handle, dC, ref pptDst, ref psize, intPtr, ref pprSrc, 0, ref pblend, 2);
                }
                finally
                {
                    WinApi.ReleaseDC(IntPtr.Zero, dC);
                    if (intPtr2 != IntPtr.Zero)
                    {
                        WinApi.SelectObject(intPtr, hObject);
                        WinApi.DeleteObject(intPtr2);
                    }

                    WinApi.DeleteDC(intPtr);
                }
            }

            private Bitmap DrawBlurBorder()
            {
                return (Bitmap)DrawOutsetShadow(0, 0, 40, 1, Color.Black,
                    new Rectangle(1, 1, ClientRectangle.Width, ClientRectangle.Height));
            }

            private Image DrawOutsetShadow(int hShadow, int vShadow, int blur, int spread, Color color,
                Rectangle shadowCanvasArea)
            {
                var rectangle = shadowCanvasArea;
                var rectangle2 = shadowCanvasArea;
                rectangle2.Offset(hShadow, vShadow);
                rectangle2.Inflate(-blur, -blur);
                rectangle.Inflate(spread, spread);
                rectangle.Offset(hShadow, vShadow);
                var rectangle3 = rectangle;
                var bitmap = new Bitmap(rectangle3.Width, rectangle3.Height, PixelFormat.Format32bppArgb);
                var graphics = Graphics.FromImage(bitmap);
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
                var cornerRadius = 0;
                do
                {
                    var num = (rectangle.Height - rectangle2.Height) / (double)(blur * 2 + spread * 2);
                    var fillColor = Color.FromArgb((int)Math.Round(200.0 * (num * num)), color);
                    var bounds = rectangle2;
                    bounds.Offset(-rectangle3.Left, -rectangle3.Top);
                    DrawRoundedRectangle(graphics, bounds, cornerRadius, Pens.Transparent, fillColor);
                    rectangle2.Inflate(1, 1);
                    cornerRadius = (int)Math.Round(blur * (1.0 - num * num));
                } while (rectangle.Contains(rectangle2));

                graphics.Flush();
                graphics.Dispose();
                return bitmap;
            }

            private void DrawRoundedRectangle(Graphics g, Rectangle bounds, int cornerRadius, Pen drawPen,
                Color fillColor)
            {
                var num = Convert.ToInt32(Math.Ceiling(drawPen.Width));
                bounds = Rectangle.Inflate(bounds, -num, -num);
                var graphicsPath = new GraphicsPath();
                if (cornerRadius > 0)
                {
                    graphicsPath.AddArc(bounds.X, bounds.Y, cornerRadius, cornerRadius, 180f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y, cornerRadius, cornerRadius,
                        270f, 90f);
                    graphicsPath.AddArc(bounds.X + bounds.Width - cornerRadius, bounds.Y + bounds.Height - cornerRadius,
                        cornerRadius, cornerRadius, 0f, 90f);
                    graphicsPath.AddArc(bounds.X, bounds.Y + bounds.Height - cornerRadius, cornerRadius, cornerRadius,
                        90f, 90f);
                }
                else
                {
                    graphicsPath.AddRectangle(bounds);
                }

                graphicsPath.CloseAllFigures();
                if (cornerRadius > 5)
                    using (var brush = new SolidBrush(fillColor))
                    {
                        g.FillPath(brush, graphicsPath);
                    }

                if (!Equals(drawPen, Pens.Transparent))
                    using (var pen = new Pen(drawPen.Color))
                    {
                        pen.EndCap = pen.StartCap = LineCap.Round;
                        g.DrawPath(pen, graphicsPath);
                    }
            }
        }
    }

    public enum MetroFormTextAlign
    {
        Left,
        Center,
        Right
    }
    public enum Messages : uint
    {
        WM_SIZE = 0x5,
        WM_GETMINMAXINFO = 0x24,
        WM_NCHITTEST = 0x84,
        WM_NCLBUTTONDOWN = 0xa1,
        WM_NCLBUTTONDBLCLK = 0xa3,
        WM_SYSCOMMAND = 0x112,
        WM_LBUTTONDBLCLK = 0x203,
        WM_DWMCOMPOSITIONCHANGED = 0x031E,

        SC_MOVE = 0xF010,
        SC_MAXIMIZE = 0xF030,
        SC_RESTORE = 0xF120,
        WM_DISPLAYCHANGE = 0x007e //分辨率调整事件
    }

    public class AutoSizeFormClass
    {
        //(1).声明结构,只记录窗体和其控件的初始位置和大小。
        public struct controlRect
        {
            public int Left;
            public int Top;
            public int Width;
            public int Height;
        }

        public controlRect InitRect { get; set; }

        //(2).声明 1个对象
        //注意这里不能使用控件列表记录 List nCtrl;，因为控件的关联性，记录的始终是当前的大小。
        //      public List oldCtrl= new List();//这里将西文的大于小于号都过滤掉了，只能改为中文的，使用中要改回西文
        public Dictionary<Control, controlRect> oldCtrl = new Dictionary<Control, controlRect>();

        //(3). 创建两个函数
        //(3.1)记录窗体和其控件的初始位置和大小,
        public void controllInitializeSize(MetroForm mForm)
        {
            controlRect cR;
            cR.Left = mForm.Left; cR.Top = mForm.Top; cR.Width = mForm.Width; cR.Height = mForm.Height;
            InitRect = cR;
            AddControl(mForm);
        }

        private void Clear()
        {
            try
            {
                var lstKeys = oldCtrl.Keys.ToList().Where(p => p == null || p.Disposing || p.IsDisposed);
                if (lstKeys.Count() > 0)
                {
                    foreach (var item in lstKeys)
                    {
                        oldCtrl.Remove(item);
                    }
                    MemoryManager.ClearMemory();
                }
            }
            catch { }
        }

        public void AddNewControl(Control ctrl)
        {
            Clear();
            if (oldCtrl.ContainsKey(ctrl))
            {
                return;
            }
            var objCtrl = new controlRect
            {
                Left = ctrl.Left,
                Top = ctrl.Top,
                Width = ctrl.Width,
                Height = ctrl.Height
            };
            oldCtrl.Add(ctrl, objCtrl);
            AddControl(ctrl);
            controlAutoSize(ctrl.Parent);
            HighDpiHelper.AttachMouseMoveEvent(ctrl);
        }

        private void AddControl(Control ctl)
        {
            foreach (Control c in ctl.Controls)
            {
                //if (c is PanelPictureView)
                //{
                //    continue;
                //}
                if (oldCtrl.ContainsKey(c))
                    continue;
                controlRect objCtrl;
                objCtrl.Left = c.Left; objCtrl.Top = c.Top; objCtrl.Width = c.Width; objCtrl.Height = c.Height;
                oldCtrl.Add(c, objCtrl);
                //**放在这里，是先记录控件本身，后记录控件的子控件
                if (c.Controls.Count > 0)
                    AddControl(c);//窗体内其余控件还可能嵌套控件(比如panel),要单独抽出,因为要递归调用
            }
        }

        //控件自适应大小
        public void controlAutoSize(Control mForm)
        {
            Form form = mForm.FindForm();
            if (form != null)
            {
                float wScale = (float)form.Width / InitRect.Width;//新旧窗体之间的比例，与最早的旧窗体
                float hScale = (float)form.Height / InitRect.Height;//.Height;
                AutoScaleControl(mForm, wScale, hScale);//窗体内其余控件还可能嵌套控件(比如panel),要单独抽出,因为要递归调用
            }
        }

        private void AutoScaleControl(Control ctl, float wScale, float hScale)
        {
            foreach (Control c in ctl.Controls)
            {
                if (!oldCtrl.ContainsKey(c))
                {
                    continue;
                }
                var oldRect = oldCtrl[c];

                // 使用SetBounds一次性设置位置和大小
                if (string.IsNullOrEmpty(c.Text) && Equals(c.GetType().Name, "MetroFormButton"))
                {
                    c.Size = new Size(
                        (int)Math.Round(oldRect.Width * (double)wScale),
                        (int)Math.Round(oldRect.Height * (double)hScale));
                }
                else
                {
                    c.SetBounds(
                        (int)Math.Round(oldRect.Left * (double)wScale),
                        (int)Math.Round(oldRect.Top * (double)hScale),
                        (int)Math.Round(oldRect.Width * (double)wScale),
                        (int)Math.Round(oldRect.Height * (double)hScale));
                }

                if (c.Controls.Count > 0)
                    AutoScaleControl(c, wScale, hScale);

                if (ctl is DataGridView gridView)
                {
                    int widths = 0;
                    for (int i = 0; i < gridView.Columns.Count; i++)
                    {
                        gridView.AutoResizeColumn(i, DataGridViewAutoSizeColumnMode.AllCells);  // 自动调整列宽  
                        widths += gridView.Columns[i].Width;   // 计算调整列后单元列的宽度和                       
                    }
                    if (widths >= gridView.Size.Width)  // 如果调整列的宽度大于设定列宽  
                        gridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;  // 调整列的模式 自动  
                    else
                        gridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;  // 如果小于 则填充  
                }
            }
        }
    }

    public static class HighDpiHelper
    {
        private static Dictionary<Form, float> dicOldDpi = new Dictionary<Form, float>();

        public static void ConvertFontByUnit(Control ctrl, GraphicsUnit unit, float scale)
        {
            if (ctrl.Font.Unit != unit)
            {
                var size = unit == GraphicsUnit.Pixel ? ctrl.Font.Size * 1.33F : ctrl.Font.Size / 1.33F;
                ctrl.Font = new Font(ctrl.Font.FontFamily.Name, size, ctrl.Font.Style, unit);
                if (ctrl is Button btn)
                {
                    if (btn.AutoSize)
                        ctrl.Size = new Size(1, 1);
                }
            }
            else
            {
                var size = ctrl.Font.Size * scale;
                ctrl.Font = new Font(ctrl.Font.FontFamily.Name, size, ctrl.Font.Style, unit);
                if (ctrl is Button btn)
                {
                    if (btn.AutoSize)
                        ctrl.Size = new Size(1, 1);
                }
            }
            foreach (Control item in ctrl.Controls)
            {
                ConvertFontByUnit(item, unit, scale);
            }
        }

        public static float GetFormDpi(Form container)
        {
            return container == null ? 1 : dicOldDpi.TryGetValue(container, out var value) ? value : container.GetDpiScale();
        }

        public static void AdjustControlImagesDpiScale(MetroForm container, bool isReset = false)
        {
            if ((!isReset && CommonString.CommonGraphicsUnit() == GraphicsUnit.Pixel)
                || !container.IsHandleCreated
                || !container.Visible
                || container.IsDisposed
                )
            {
                return;
            }

            if (container.WindowState == FormWindowState.Minimized)
            {
                container.WindowState = FormWindowState.Normal;
                Application.DoEvents();
            }

            try
            {
                // 暂停布局和绘制
                container.SuspendLayout();

                // 记录原始位置和大小
                var originalBounds = container.Bounds;

                // 计算DPI缩放
                var dpiScale = isReset ? 1F : container.GetDpiScale();
                var isOldForm = dicOldDpi.ContainsKey(container);
                var oldDpi = dicOldDpi.TryGetValue(container, out var value) ? value : 1f;
                dicOldDpi[container] = dpiScale;
                if (Equals(dpiScale, oldDpi))
                {
                    return;
                }

                var scale = dpiScale / oldDpi;

                // 转换字体
                ConvertFontByUnit(container, CommonString.CommonGraphicsUnit(), isOldForm ? scale : 1);

                // 使用SetBounds而不是直接修改Size - 直接计算避免中间变量
                int newWidth = (int)Math.Round(container.InitForm.InitRect.Width * (double)dpiScale);
                int newHeight = (int)Math.Round(container.InitForm.InitRect.Height * (double)dpiScale);

                // 计算并设置新尺寸（保持位置不变）
                // GetStartPosition确保窗体在屏幕中央
                Point centerPosition = new Point(
                    (Screen.FromControl(container).WorkingArea.Width - newWidth) / 2 + Screen.FromControl(container).WorkingArea.X,
                    (Screen.FromControl(container).WorkingArea.Height - newHeight) / 2 + Screen.FromControl(container).WorkingArea.Y
                );

                // 一次性设置新的位置和尺寸
                container.SetBounds(centerPosition.X, centerPosition.Y, newWidth, newHeight);

                // 内部控件调整
                container.InitForm.controlAutoSize(container);
                container.OnThemeChange();

                // 调整其他控件
                AdjustControlImagesDpiScale(new List<Control> { container }, scale, dpiScale);

                // 更新窗口按钮
                container.UpdateWindowButtonImage();
                container.UpdateWindowButtonPosition();
            }
            finally
            {
                // 恢复布局
                container.ResumeLayout(true);

                // 强制完整重绘
                container.Invalidate(true);
                container.Update();
            }
        }

        private static readonly List<string> lstExpMouseMoveType = new List<string> { "ImageBox", "DualModeImageViewer", "SmartWebControl" };
        public static void AttachMouseMoveEvent(Control control)
        {
            // 判断控件是否有 Image 属性
            if (control.GetType().GetProperty("Image") != null && !lstExpMouseMoveType.Any(p => control.GetType().ToString().Contains(p)))
            {
                if (!Equals(control.AccessibleDefaultActionDescription, "webloading") && !Equals(control.Name, "pbThumbnail"))
                {
                    control.MouseHover -= AutoScaleWhenMove;
                    control.MouseHover += AutoScaleWhenMove;
                }
            }
            //if (control.GetType().GetEvent("Click") != null)
            //{
            //    control.MouseDown -= AnimationManager.DoAnimation;
            //    control.MouseDown += AnimationManager.DoAnimation;
            //}

            // 遍历控件的子控件并递归调用 AttachMouseMoveEvent 方法
            foreach (Control childControl in control.Controls)
            {
                AttachMouseMoveEvent(childControl);
            }

            if (control is ToolStrip toolStrip)
            {
                AttachMouseMoveEvent(toolStrip.Items);
            }
            if (control.ContextMenuStrip != null)
            {
                AttachMouseMoveEvent(control.ContextMenuStrip.Items);
            }
        }

        private static void AttachMouseMoveEvent(ToolStripItemCollection items)
        {
            foreach (ToolStripItem item in items)
            {
                if (item.GetType().GetProperty("Image") != null
                    && (string.IsNullOrEmpty(item.Name) || !item.Name.StartsWith("cmsTrans")))
                {
                    // 1. 先移除事件避免重复添加
                    item.MouseHover -= AutoScaleWhenMove;

                    // 2. 安全处理可能有问题的图标
                    try
                    {
                        // 确保图标不是损坏的或格式不兼容的
                        if (item.Image != null)
                        {
                            // 尝试访问可能导致问题的属性，提前捕获异常
                            try
                            {
                                var dimensions = item.Image.FrameDimensionsList;
                                // 如果没有抛出异常，图像是安全的，可以添加事件
                                item.MouseHover += AutoScaleWhenMove;
                            }
                            catch
                            {
                                // 图像有问题，不添加动画事件
                            }
                        }
                        else
                        {
                            // 没有图像的项目是安全的
                            item.MouseHover += AutoScaleWhenMove;
                        }
                    }
                    catch
                    {
                        // 如果获取Image属性也失败，不添加事件
                    }
                }

                // 如果该项是一个下拉菜单，则递归调用 AttachMouseMoveEvent 方法为其子项添加事件
                if (item is ToolStripMenuItem menuItem && menuItem.DropDownItems.Count > 0)
                {
                    AttachMouseMoveEvent(menuItem.DropDownItems);
                }
                if (item is ToolStripDropDownButton dropDownButton && dropDownButton.DropDownItems.Count > 0)
                {
                    AttachMouseMoveEvent(dropDownButton.DropDownItems);
                }
            }
        }

        private static void AutoScaleWhenMove(object sender, EventArgs e)
        {
            // 对其他控件使用新的动画逻辑
            ImageAnimatorHelper.HandleMouseMoveAnimation(sender, e);
        }

        public static void AdjustControlImagesDpiScale(IEnumerable controls, float scale, float dpiScale)
        {
            foreach (Control control in controls)
            {
                //if (control is UcContent)
                //{
                //    continue;
                //}
                switch (control)
                {
                    case Button button:
                        if (button.Image != null)
                        {
                            button.Image = GetControlImage(button.AccessibleDefaultActionDescription, button.Image, scale, dpiScale);
                        }
                        //if (button.BackgroundImage != null)
                        //{
                        //    button.BackgroundImage = ImageProcessHelper.ScaleImage(button.BackgroundImage, scale);
                        //}
                        break;
                    case PictureBox pic:
                        if (pic.Image != null && pic.SizeMode != PictureBoxSizeMode.StretchImage
                            && !Equals(pic.AccessibleDefaultActionDescription, "webloading"))
                            pic.Image = GetControlImage(pic.AccessibleDefaultActionDescription, pic.Image, scale, dpiScale);
                        else if (pic.BackgroundImage != null)
                            pic.BackgroundImage = GetControlImage(pic.AccessibleDefaultActionDescription, pic.BackgroundImage, scale, dpiScale);
                        break;
                    case ToolStrip toolStrip:
                        ScaleToolStrip(scale, dpiScale, toolStrip);
                        break;
                }

                if (control.ContextMenuStrip != null)
                {
                    ScaleToolStrip(scale, dpiScale, control.ContextMenuStrip);
                }

                // Then recurse
                AdjustControlImagesDpiScale(control.Controls, scale, dpiScale);
            }
        }

        private static Image GetControlImage(string resource, Image image, float scale, float dpiScale)
        {
            Bitmap bitmap = null;
            if (!string.IsNullOrEmpty(resource))
            {
                try
                {
                    bitmap = ImageProcessHelper.ScaleImage(ImageProcessHelper.GetResourceImage(resource), dpiScale);
                }
                catch { }
            }
            try
            {
                if (bitmap == null)
                    bitmap = ImageProcessHelper.ScaleImage(image, scale);
            }
            catch { }
            return bitmap;
        }

        public static void ScaleToolStrip(float scale, float dpiScale, ToolStrip toolStrip)
        {
            // 基准图标大小，默认为 16x16 像素 - 使用高精度计算
            int targetSize = (int)Math.Round(16 * (double)dpiScale);
            toolStrip.ImageScalingSize = new Size(targetSize, targetSize);

            ScaleToolStripMenuItem(scale, dpiScale, toolStrip.Items);
            toolStrip.AutoSize = !toolStrip.AutoSize;
            toolStrip.AutoSize = !toolStrip.AutoSize;

            //toolStrip.Size = new Size(toolStrip.Width + 5, toolStrip.Height + 5);
        }

        private static void ScaleToolStripMenuItem(float scale, float dpiScale, ToolStripItemCollection items)
        {
            foreach (ToolStripItem item in items)
            {
                if (item.Image != null && item.BackgroundImage == null)
                    try
                    {
                        //item.AutoSize = false;
                        //item.ImageScaling = ToolStripItemImageScaling.SizeToFit;
                        item.Image = GetControlImage(item.AccessibleDefaultActionDescription, item.Image, scale, dpiScale);
                        //item.Size = new Size(item.Width + 5, item.Height);
                        //if (item.Image != null)
                        //{
                        //    item.ImageScaling = ToolStripItemImageScaling.None;
                        //}
                    }
                    catch { }
                if (item is ToolStripMenuItem menuItem && menuItem.DropDownItems.Count > 0)
                {
                    ScaleToolStripMenuItem(scale, dpiScale, menuItem.DropDownItems);
                }
                if (item is ToolStripDropDownButton dropDownButton && dropDownButton.DropDownItems.Count > 0)
                {
                    ScaleToolStripMenuItem(scale, dpiScale, dropDownButton.DropDownItems);
                }
            }
        }
    }
}